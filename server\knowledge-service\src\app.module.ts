import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ElasticsearchModule } from '@nestjs/elasticsearch';

// 实体
import { KnowledgeBase } from './entities/knowledge-base.entity';
import { KnowledgeDocument } from './entities/knowledge-document.entity';
import { DocumentChunk } from './entities/document-chunk.entity';
import { DigitalHuman } from './entities/digital-human.entity';
import { DigitalHumanKnowledgeBinding } from './entities/digital-human-knowledge-binding.entity';

// 模块
import { DatabaseModule } from './database/database.module';
import { CacheModule } from './cache/cache.module';
import { StorageModule } from './storage/storage.module';
import { ProcessingModule } from './processing/processing.module';
import { UploadModule } from './upload/upload.module';
import { KnowledgeBasesModule } from './modules/knowledge-bases/knowledge-bases.module';
import { DocumentsModule } from './modules/documents/documents.module';
import { DigitalHumansModule } from './modules/digital-humans/digital-humans.module';
import { BindingsModule } from './modules/bindings/bindings.module';
import { AuthModule } from './modules/auth/auth.module';
import { HealthModule } from './modules/health/health.module';

// 控制器
import { HealthController } from './common/controllers/health.controller';

// 配置
import productionConfig from './config/production.config';

// 服务
import { LoggerService } from './common/services/logger.service';
import { AppService } from './app.service';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      load: [productionConfig],
    }),
    
    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get('DB_HOST', 'localhost'),
        port: configService.get('DB_PORT', 3306),
        username: configService.get('DB_USERNAME', 'root'),
        password: configService.get('DB_PASSWORD', 'password'),
        database: configService.get('DB_DATABASE', 'knowledge_service'),
        entities: [
          KnowledgeBase,
          KnowledgeDocument,
          DocumentChunk,
          DigitalHuman,
          DigitalHumanKnowledgeBinding,
        ],
        synchronize: configService.get('NODE_ENV') !== 'production',
        logging: configService.get('NODE_ENV') === 'development',
        charset: 'utf8mb4',
        timezone: '+08:00',
        extra: {
          authPlugin: 'mysql_native_password',
          ssl: false,
          connectionLimit: 10,
          acquireTimeout: 60000,
          timeout: 60000,
        },
      }),
      inject: [ConfigService],
    }),
    
    // Elasticsearch模块
    ElasticsearchModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const config: any = {
          node: configService.get('ELASTICSEARCH_URL', 'http://localhost:9200'),
        };

        // 只有在用户名和密码都存在且不为空时才添加认证信息
        const username = configService.get('ELASTICSEARCH_USERNAME');
        const password = configService.get('ELASTICSEARCH_PASSWORD');

        if (username && password && username.trim() !== '' && password.trim() !== '') {
          config.auth = {
            username,
            password,
          };
        }

        return config;
      },
      inject: [ConfigService],
    }),
    
    // JWT模块
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET', 'knowledge-service-secret'),
        signOptions: { expiresIn: '24h' },
      }),
      inject: [ConfigService],
    }),
    
    // Passport模块
    PassportModule.register({ defaultStrategy: 'jwt' }),
    
    // 微服务客户端
    ClientsModule.registerAsync([
      {
        name: 'SERVICE_REGISTRY',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('SERVICE_REGISTRY_HOST', 'localhost'),
            port: configService.get<number>('SERVICE_REGISTRY_PORT', 3010),
          },
        }),
        inject: [ConfigService],
      },
    ]),
    
    // 核心模块
    DatabaseModule,
    CacheModule,
    StorageModule,
    ProcessingModule,
    UploadModule,
    
    // 业务模块
    KnowledgeBasesModule,
    DocumentsModule,
    DigitalHumansModule,
    BindingsModule,
    AuthModule,
    HealthModule,
  ],
  controllers: [HealthController],
  providers: [AppService, LoggerService],
  exports: [LoggerService],
})
export class AppModule {}
