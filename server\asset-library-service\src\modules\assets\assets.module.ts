import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ElasticsearchModule } from '@nestjs/elasticsearch';
import { AssetsService } from './assets.service';
import { AssetsController } from './assets.controller';
import { Asset } from './entities/asset.entity';
import { Category } from '../categories/entities/category.entity';
import { Tag } from '../tags/entities/tag.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Asset, Category, Tag]),
    ElasticsearchModule,
  ],
  controllers: [AssetsController],
  providers: [
    AssetsService,
  ],
  exports: [AssetsService],
})
export class AssetsModule {}
